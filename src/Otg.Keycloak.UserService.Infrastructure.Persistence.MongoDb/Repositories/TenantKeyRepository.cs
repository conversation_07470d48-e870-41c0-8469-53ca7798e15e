using Otg.Keycloak.UserService.Application.Common.Interfaces.Persistence;
using Otg.Keycloak.UserService.Domain.Aggregates;
using ErrorOr;
using Otg.Keycloak.UserService.Infrastructure.Persistence.MongoDb.Common;
using Otg.Hcm.Infrastructure.Persistence.MongoDb;
using System.Diagnostics;
using Otg.Keycloak.UserService.Domain.ValueObjects;
using Otg.Hcm.Domain.ValueObjects;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Otg.Keycloak.UserService.Application.Common.Errors;

namespace Otg.Keycloak.UserService.Infrastructure.Persistence.MongoDb.Repositories;

public class TenantKeyRepository(IHcmRepositoryFactory repositoryFactory) : ITenantKeyRepository
{
    private readonly IHcmRepository<TenantKey, TenantKeyId> _repository =
        repositoryFactory.CreateMultiTenantRepository<TenantKey, TenantKeyId>();

    #region TenantKey
    /// <summary>
    /// Upserts a tenant key (creates if not exists, updates if exists)
    /// </summary>
    public async Task<ErrorOr<TenantKey>> UpsertKeyAsync(TenantKey tenantKey, CancellationToken cancellationToken = default)
    {
        // Create a new activity to enable tracing this method
        using var activity = Common.Tracing.ActivitySources.InfrastructureLayerSource.StartActivity(Common.Tracing.WellKnownActivities.TenantKeyRepository.UpsertTenantKey);

        try
        {
            tenantKey.UpdatedAt = DateTime.UtcNow;

            // Check if the key already exists
            var existingKeyResult = GetKeyForTenantAsync(tenantKey.TenantId.Value, cancellationToken);

            if (!existingKeyResult.IsError)
            {
                // Update existing key
                var existingKey = existingKeyResult.Value!;

                var builder = _repository.AggregateRootUpdateBuilder(existingKey.Id);
                builder.Set(k => k.PublicKey, tenantKey.PublicKey);
                builder.Set(k => k.EncryptedPrivateKey, tenantKey.EncryptedPrivateKey);
                builder.Set(k => k.UpdatedAt, DateTime.UtcNow);

                var result = await _repository.UpdateAggregateRootAsync(builder, cancellationToken);
                return result.MatchFirst<ErrorOr<TenantKey>>(
                    data => tenantKey,
                    error => Error.Failure("TenantKey.UpdateFailed", $"Error: {error.Code} - {error.Description}")
                );
            }

            if (existingKeyResult.FirstError.Type == ErrorType.NotFound)
            {
                tenantKey.CreatedAt = DateTime.UtcNow;
                tenantKey.UpdatedAt = DateTime.UtcNow;
                
                tenantKey.Id = TenantKeyId.Create();

                try
                {
                    var addResult = await _repository.AddAggregateRootV2Async(tenantKey, cancellationToken);

                    return addResult.MatchFirst<ErrorOr<TenantKey>>(
                        addedData => addedData,
                        error => Error.Failure("TenantKey.CreateFailed", $"Error code: {error.Code}, Description: {error.Description}")
                    );
                }
                catch (MongoDB.Driver.MongoConnectionException connEx)
                {
                    return Error.Failure("TenantKey.ConnectionFailed", $"MongoDB connection error: {connEx.Message}");
                }
                catch (MongoDB.Driver.MongoWriteException writeEx)
                {
                    return Error.Failure("TenantKey.WriteFailed", $"MongoDB write error: {writeEx.WriteError.Category} - {writeEx.WriteError.Message}");
                }
                catch (Exception ex)
                {
                    return Error.Failure("TenantKey.CreateFailed", $"Exception type: {ex.GetType().Name}, Message: {ex.Message}");
                }
            }

            return existingKeyResult.Errors;
        }
        catch (Exception ex)
        {
            return Error.Failure("TenantKey.UpsertFailed", $"Unhandled exception: {ex.GetType().Name} - {ex.Message}");
        }
    }

    /// <summary>
    /// Gets the key for a tenant
    /// </summary>
    public ErrorOr<TenantKey?> GetKeyForTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var keysQueryable = _repository.GetAllAggregateRootsAsQueryable();
            return keysQueryable.FirstOrDefault(c => c.TenantId.Value.Equals(tenantId, StringComparison.CurrentCultureIgnoreCase));
        }
        catch (Exception ex)
        {
            return Errors.TenantKeyOperations.TenantKeyNotFound;
        }
    }
    #endregion
}
