using System.Security.Cryptography;
using Microsoft.Extensions.Configuration;
using Otg.Keycloak.UserService.Application.Common.Services;

namespace Otg.Keycloak.UserService.Application.UnitTests.Common.Services;

public class KeyEncryptionServiceTests : IDisposable
{
    private readonly IConfiguration _validConfiguration;
    private readonly IConfiguration _invalidConfiguration;
    private readonly IConfiguration _missingKeyConfiguration;
    private readonly string _testMasterKey;
    private readonly string _testPrivateKey;

    public KeyEncryptionServiceTests()
    {
        // Generate a test master key (32 bytes = 256 bits for AES-256)
        var masterKeyBytes = new byte[32];
        RandomNumberGenerator.Fill(masterKeyBytes);
        _testMasterKey = Convert.ToHexString(masterKeyBytes);

        // Valid configuration
        var validConfigData = new Dictionary<string, string>
        {
            ["KeyEncryption:MasterKey"] = _testMasterKey
        };
        _validConfiguration = new ConfigurationBuilder()
            .AddInMemoryCollection(validConfigData!)
            .Build();

        // Invalid hex string configuration
        var invalidConfigData = new Dictionary<string, string>
        {
            ["KeyEncryption:MasterKey"] = "invalid-hex-string"
        };
        _invalidConfiguration = new ConfigurationBuilder()
            .AddInMemoryCollection(invalidConfigData!)
            .Build();

        // Missing key configuration
        var missingKeyConfigData = new Dictionary<string, string>();
        _missingKeyConfiguration = new ConfigurationBuilder()
            .AddInMemoryCollection(missingKeyConfigData!)
            .Build();

        // Sample RSA private key for testing
        _testPrivateKey = @"-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wQNfFr8HtmRzJxVfKo0CwHBKtLAcGjxh1w2xnOtAGm6sAiGtVhRhcV1CwA5sBBxQ
-----END PRIVATE KEY-----";
    }

    [Fact]
    public void Constructor_WithValidConfiguration_ShouldCreateInstance()
    {
        // Act & Assert
        var service = new KeyEncryptionService(_validConfiguration);
        service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithMissingMasterKey_ShouldThrowInvalidOperationException()
    {
        // Act & Assert
        var act = () => new KeyEncryptionService(_missingKeyConfiguration);
        act.Should().Throw<InvalidOperationException>()
           .WithMessage("MasterKey missing");
    }

    [Fact]
    public void Constructor_WithInvalidHexString_ShouldThrowFormatException()
    {
        // Act & Assert
        var act = () => new KeyEncryptionService(_invalidConfiguration);
        act.Should().Throw<FormatException>();
    }

    [Fact]
    public void EncryptPrivateKey_WithValidInput_ShouldReturnBase64String()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var privateKey = "test-private-key";

        // Act
        var result = service.EncryptPrivateKey(privateKey);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        // Verify it's valid Base64
        var act = () => Convert.FromBase64String(result);
        act.Should().NotThrow();
    }

    [Fact]
    public void EncryptPrivateKey_WithSameInput_ShouldProduceDifferentOutputs()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var privateKey = "test-private-key";

        // Act
        var result1 = service.EncryptPrivateKey(privateKey);
        var result2 = service.EncryptPrivateKey(privateKey);

        // Assert
        result1.Should().NotBe(result2, "encryption should use random salt and nonce");
    }

    [Fact]
    public void EncryptPrivateKey_WithEmptyString_ShouldReturnValidEncryption()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var privateKey = string.Empty;

        // Act
        var result = service.EncryptPrivateKey(privateKey);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        // Verify it's valid Base64
        var act = () => Convert.FromBase64String(result);
        act.Should().NotThrow();
    }

    [Fact]
    public void EncryptPrivateKey_WithRealPrivateKey_ShouldReturnValidEncryption()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);

        // Act
        var result = service.EncryptPrivateKey(_testPrivateKey);

        // Assert
        result.Should().NotBeNullOrEmpty();
        
        // Verify it's valid Base64
        var act = () => Convert.FromBase64String(result);
        act.Should().NotThrow();
    }

    [Fact]
    public void DecryptPrivateKey_WithValidEncryptedData_ShouldReturnOriginalText()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var originalText = "test-private-key";
        var encrypted = service.EncryptPrivateKey(originalText);

        // Act
        var decrypted = service.DecryptPrivateKey(encrypted);

        // Assert
        decrypted.Should().Be(originalText);
    }

    [Fact]
    public void EncryptDecrypt_RoundTrip_WithEmptyString_ShouldWork()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var originalText = string.Empty;

        // Act
        var encrypted = service.EncryptPrivateKey(originalText);
        var decrypted = service.DecryptPrivateKey(encrypted);

        // Assert
        decrypted.Should().Be(originalText);
    }

    [Fact]
    public void EncryptDecrypt_RoundTrip_WithRealPrivateKey_ShouldWork()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);

        // Act
        var encrypted = service.EncryptPrivateKey(_testPrivateKey);
        var decrypted = service.DecryptPrivateKey(encrypted);

        // Assert
        decrypted.Should().Be(_testPrivateKey);
    }

    [Fact]
    public void DecryptPrivateKey_WithInvalidBase64_ShouldThrowFormatException()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var invalidBase64 = "invalid-base64-string!@#";

        // Act & Assert
        var act = () => service.DecryptPrivateKey(invalidBase64);
        act.Should().Throw<FormatException>();
    }

    [Fact]
    public void DecryptPrivateKey_WithCorruptedData_ShouldThrowCryptographicException()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var originalText = "test-private-key";
        var encrypted = service.EncryptPrivateKey(originalText);
        
        // Corrupt the encrypted data by changing a few bytes
        var encryptedBytes = Convert.FromBase64String(encrypted);
        encryptedBytes[^1] ^= 0xFF; // Flip bits in the last byte
        var corruptedEncrypted = Convert.ToBase64String(encryptedBytes);

        // Act & Assert
        var act = () => service.DecryptPrivateKey(corruptedEncrypted);
        act.Should().Throw<CryptographicException>();
    }

    [Fact]
    public void DecryptPrivateKey_WithWrongMasterKey_ShouldThrowCryptographicException()
    {
        // Arrange
        using var service1 = new KeyEncryptionService(_validConfiguration);
        var originalText = "test-private-key";
        var encrypted = service1.EncryptPrivateKey(originalText);

        // Create service with different master key
        var differentMasterKeyBytes = new byte[32];
        RandomNumberGenerator.Fill(differentMasterKeyBytes);
        var differentMasterKey = Convert.ToHexString(differentMasterKeyBytes);
        
        var differentConfigData = new Dictionary<string, string>
        {
            ["KeyEncryption:MasterKey"] = differentMasterKey
        };
        var differentConfiguration = new ConfigurationBuilder()
            .AddInMemoryCollection(differentConfigData!)
            .Build();

        using var service2 = new KeyEncryptionService(differentConfiguration);

        // Act & Assert
        var act = () => service2.DecryptPrivateKey(encrypted);
        act.Should().Throw<CryptographicException>();
    }

    [Fact]
    public void EncryptedData_ShouldHaveCorrectStructure()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var privateKey = "test-private-key";

        // Act
        var encrypted = service.EncryptPrivateKey(privateKey);
        var encryptedBytes = Convert.FromBase64String(encrypted);

        // Assert
        // Structure: [salt(16)|nonce(12)|ciphertext|tag(16)]
        var expectedMinLength = 16 + 12 + privateKey.Length + 16; // salt + nonce + ciphertext + tag
        encryptedBytes.Length.Should().Be(expectedMinLength);
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Arrange
        var service = new KeyEncryptionService(_validConfiguration);

        // Act & Assert
        var act = () => service.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public void EncryptPrivateKey_WithLargeInput_ShouldWork()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var largeInput = new string('A', 10000); // 10KB of data

        // Act
        var encrypted = service.EncryptPrivateKey(largeInput);
        var decrypted = service.DecryptPrivateKey(encrypted);

        // Assert
        decrypted.Should().Be(largeInput);
    }

    [Fact]
    public void EncryptPrivateKey_WithUnicodeCharacters_ShouldWork()
    {
        // Arrange
        using var service = new KeyEncryptionService(_validConfiguration);
        var unicodeText = "Hello 世界 🌍 Ñoño";

        // Act
        var encrypted = service.EncryptPrivateKey(unicodeText);
        var decrypted = service.DecryptPrivateKey(encrypted);

        // Assert
        decrypted.Should().Be(unicodeText);
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
